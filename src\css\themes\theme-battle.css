/* GamePort - Battle Theme (烈焰战场 - 红橙色系) */
/* 火红战斗风格，激烈战斗竞技氛围 */

:root {
  /* 主要颜色变量 - 红橙色系 */
  --primary-color: #dc2626;
  --primary-hover: #b91c1c;
  --primary-light: #fecaca;
  --secondary-color: #991b1b;
  --accent-color: #ea580c;
  
  /* 背景颜色 */
  --bg-primary: #fef2f2;
  --bg-secondary: #fee2e2;
  --bg-tertiary: #fecaca;
  --bg-dark: #7f1d1d;
  
  /* Header专用背景色 - 比主背景更深 */
  --header-bg-color: #fee2e2;
  
  /* 文字颜色 */
  --text-primary: #7f1d1d;
  --text-secondary: #991b1b;
  --text-light: #dc2626;
  --text-white: #ffffff;
  
  /* 辅助颜色变量 */
  --border-color: #fecaca;
  --border-hover: #fca5a5;
  --shadow-color: rgba(220, 38, 38, 0.2);
  --shadow-hover: rgba(220, 38, 38, 0.3);
  
  /* 状态颜色 */
  --success-color: #059669;
  --warning-color: #f59e0b;
  --error-color: #dc2626;
  --info-color: #3b82f6;
  
  /* 按钮颜色状态 */
  --btn-primary-bg: #dc2626;
  --btn-primary-hover: #b91c1c;
  --btn-primary-active: #991b1b;
  --btn-secondary-bg: #ea580c;
  --btn-secondary-hover: #c2410c;
  
  /* 链接颜色 */
  --link-color: #dc2626;
  --link-hover: #b91c1c;
  --link-visited: #991b1b;
  
  /* 特殊效果颜色 */
  --gradient-primary: linear-gradient(135deg, #dc2626 0%, #ea580c 100%);
  --gradient-secondary: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}
